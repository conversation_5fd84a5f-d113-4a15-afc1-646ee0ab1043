{% extends "base.jinja2" %}

{% block title %}Sign Up{% endblock %}
{% block content %}
<div id="content-div">

  {# TITLE SINGLE #}
  <div>
    <style>
        me {
          width: 100%;
          text-align: center;
          padding-bottom: 8px;
        }

        me div {
          color: var(--color-text-title);
          padding-bottom: 14px;
          font-family: 'Noto Serif', serif;
          font-size: 24px;
          font-weight: 400;
          font-stretch: semi-condensed;
          font-style: italic;
        }

        me hr {
          display: block;
          height: 1px;
          border: 0;
          border-top: 1px solid var(--color-hr-lines);
          margin: 0;
          padding: 0;
        }
    </style>
    <div>Sign Up</div>
    <hr />
  </div>

  <form data-signals="{_users_submit_button_disable:false, _form_invalid:true}" data-on-submit="$_users_submit_button_disable = true;@post('/signup_validate', {contentType: 'form'})">

    <div data-on-input="let email = document.getElementById('signupemail'); let pass = document.getElementById('signuppass'); let passrepeat = document.getElementById('signuppassrepeat'); let terms = document.getElementById('terms-checkbox'); $_form_invalid = !(email?.checkValidity() && pass?.checkValidity() && passrepeat?.checkValidity() && terms?.checked)">
      {{ render_partial('partials/forms-input.jinja2', namealwayschange='signupemail', label='Email', type = 'email', pattern="^[^@]+@[^@]+\.[^@]+$", errormessage="Invalid email address") }}
      {{ render_partial('partials/forms-input-password.jinja2', namealwayschange='signuppass', label='Password') }}
      {{ render_partial('partials/forms-input-password.jinja2', namealwayschange='signuppassrepeat', label='Confirm Password') }}
    </div>

    {# TERMS AGREEMENT CHECKBOX #}
    <div>
      <style>
        me {
          display: flex;
          align-items: center;
          margin-top: 34px;
          margin-bottom: 0px;
        }

        me input[type="checkbox"] {
          width: 18px;
          height: 18px;
          margin-right: 12px;
          cursor: pointer;
          accent-color: var(--color-text-dark);
        }

        me label {
          color: var(--color-text-black);
          font-family: 'Noto Sans', sans-serif;
          font-size: 16px;
          font-weight: 300;
          cursor: pointer;
          line-height: 1.4;
        }

        me a {
          color: var(--color-text-dark);
          text-decoration: none;
          font-weight: 400;
          cursor: pointer;
        }

        me a:hover {
          text-decoration: underline;
        }
      </style>
      <input type="checkbox"
             id="terms-checkbox"
             name="terms_agreed"
             data-on-change="let email = document.getElementById('signupemail'); let pass = document.getElementById('signuppass'); let passrepeat = document.getElementById('signuppassrepeat'); let terms = document.getElementById('terms-checkbox'); $_form_invalid = !(email?.checkValidity() && pass?.checkValidity() && passrepeat?.checkValidity() && terms?.checked)" />
      <label for="terms-checkbox">
        I agree to the <a onclick="showPrivacyPolicy()">Privacy Policy</a> and <a onclick="showTermsConditions()">Terms and Conditions</a>
      </label>
    </div>

    {# SUBMIT BUTTON #}
    <button type="submit" data-attr-disabled="$_form_invalid || $_users_submit_button_disable">
      <style>
          me {
              height: 40px;
              margin-top: 34px;
              width: 100%;
              display: block;
              background-color: transparent;
              color: var(--color-text-dark);
              border: 1px solid var(--color-text-dark);
              cursor: pointer;
              border-width: 1px;
              border-radius: 6px;
              font-family: 'Noto Sans', sans-serif;
              font-weight: 500;
              font-size: 18px;
              font-stretch: semi-condensed;
              text-align: center;
              transition: background-color 0.3s ease, color 0.3s ease;
          }

          me:hover {
              background-color: var(--color-background-dark);
              color: var(--color-text-bright);
          }

          me:disabled {
              background-color: var(--color-disabled);
              color: var(--color-text-brighter);
              opacity: 0.6;
              cursor: not-allowed;
          }

          me .button-spinner {
              display: none;
              width: 30px;
              height: 30px;
          }
      </style>
      <span class="button-text" data-attr-style="$_users_submit_button_disable ? 'display: none' : 'display: inline'">Submit</span>
      <img class="button-spinner" data-attr-style="$_users_submit_button_disable ? 'display: inline; margin-top: 4px; margin-bottom: 0px;' : 'display: none'"
            src="{{ url_for('static', path='/images/tube-spinner.svg') }}"
            alt="spinning..." />
    </button>

  </form>

  <div id="errordiv"></div>

  {# Not registered yet? #}
  <div>
    <style>
        me {
            margin-top: 99px;
            margin-bottom: 0px;
            text-align: center;
        }

        me a {
            color: var(--color-text-dark);
            text-decoration: none;
            font-weight: 400;
            font-stretch: semi-condensed;
        }

        me a:hover {
            text-decoration: underline;
            cursor: pointer;
        }
    </style>
    Already have an account?
    <a href="/login_form">Log in</a>
  </div>

  <div popover id="password-info-popover">
    <style>
        me {
            width: 400px;
            background-color: var(--color-background-dark);
            color: var(--color-text-bright);
            border: 1px solid var(--color-background-middle);
            border-width: 1px;
            border-radius: 16px;
            padding: 38px 38px;
            cursor: pointer;
            animation: fadeIn 0.26s ease-in;
            text-align: left;
            font-size: 18px;
        }

        me ul {
            list-style-type: disc
        }
    </style>
    The password should:
    <ul>
      <li>Be 8 to 64 characters long</li>
      <li>Contain at least one digit</li>
      <li>Contain at least one lowercase letter</li>
      <li>Contain at least one uppercase letter</li>
      <li>Contain at least one special character</li>
    </ul>
  </div>

  {# PRIVACY POLICY MODAL #}
  <div id="privacy-policy-modal" style="display: none;">
    <style>
      me {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    </style>
    <div onclick="event.stopPropagation()">
      <style>
        me {
          all: unset;
          width: calc(100vw - 100px);
          max-width: 720px;
          max-height: 80vh;
          border: 1px solid var(--color-background-dark);
          border-radius: 11px;
          display: block;
          padding: 0;
          font-family: 'Noto Sans', sans-serif;
          background-color: var(--color-background-bright);
          position: relative;
        }
      </style>

      {# CLOSE BUTTON #}
      <div onclick="hidePrivacyPolicy()">
        <style>
          me {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
          }

          me .close-x {
            width: 26px;
            height: 2px;
            background-color: var(--color-text-dark);
            position: relative;
            transform: rotate(45deg);
            transition: all 0.3s ease;
          }

          me .close-x::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 2px;
            background-color: var(--color-text-dark);
            left: 0;
            top: 0;
            transform: rotate(-90deg);
            transition: all 0.3s ease;
          }

          me:hover .close-x,
          me:hover .close-x::after {
            background-color: greenyellow;
          }
        </style>
        <div class="close-x"></div>
      </div>

      {# TITLE #}
      <div>
        <style>
          me {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            background-color: var(--color-background-bright);
            color: var(--color-text-dark);
            height: 40px;
            text-align: center;
            line-height: 40px;
            vertical-align: middle;
            font-family: 'Noto Serif', serif;
            font-size: 20px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 8px;
          }
        </style>
        Privacy Policy
      </div>

      {# CONTENT #}
      <div>
        <style>
          me {
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
            background-color: var(--color-background-bright);
            padding: 15px 20px;
            text-align: left;
            font-size: 16px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            color: var(--color-text-black);
            max-height: calc(80vh - 80px);
            overflow-y: auto;
          }

          me h2 {
            color: var(--color-text-dark);
            font-family: 'Noto Serif', serif;
            font-size: 22px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 16px;
          }

          me h3 {
            color: var(--color-text-dark);
            font-family: 'Noto Serif', serif;
            font-size: 18px;
            font-weight: 500;
            margin-top: 20px;
            margin-bottom: 12px;
          }

          me p {
            margin-bottom: 12px;
            line-height: 1.5;
          }

          me ul {
            margin-bottom: 12px;
            padding-left: 20px;
          }

          me li {
            margin-bottom: 6px;
            line-height: 1.4;
          }
        </style>
        <h2>Privacy Policy</h2>
        <p><strong>Last updated:</strong> 10 July 2025</p>

        <p>We are committed to protecting your privacy and handling your data transparently and responsibly, in accordance with the General Data Protection Regulation (GDPR).</p>

        <h3>1. What data we collect</h3>
        <p>When you register or use our service, we collect the following personal data:</p>
        <ul>
          <li>Your email address</li>
          <li>Your encrypted password</li>
          <li>Your IP address (temporarily, for security and abuse prevention)</li>
        </ul>
        <p>Optionally, if you use our PDF report feature, you may also provide:</p>
        <ul>
          <li>Information about your customers (e.g., names, services, prices), only for inclusion in your reports</li>
        </ul>

        <h3>2. Purpose of data collection</h3>
        <p>We collect and process this data only to:</p>
        <ul>
          <li>Create and manage your account</li>
          <li>Enable login, password reset, and security features</li>
          <li>Generate PDF reports based on the data you enter</li>
          <li>Prevent misuse of our service</li>
        </ul>
        <p>We do <strong>not</strong> use your data or your customers' data for any other purpose. We do <strong>not</strong> sell, share, or analyze this data in any way.</p>

        <h3>3. Legal basis for processing</h3>
        <ul>
          <li><strong>Consent</strong> – when you sign up and agree to this Privacy Policy</li>
          <li><strong>Contractual necessity</strong> – to provide the service you requested (e.g., PDF generation)</li>
        </ul>

        <h3>4. Data storage and retention</h3>
        <ul>
          <li>Your account data is stored securely and retained as long as your account is active.</li>
          <li>You may delete your account at any time, and we will erase your personal data.</li>
          <li>Optional customer data used in reports is stored only as long as needed for functionality and is never reused or accessed for other purposes.</li>
        </ul>

        <h3>5. Cookies</h3>
        <p>We use only essential cookies for the operation of our service, such as:</p>
        <ul>
          <li>Session cookies (for login)</li>
          <li>Cookies for password reset and account security</li>
        </ul>
        <p>No tracking, analytics, or advertising cookies are used.</p>

        <h3>6. Your rights under GDPR</h3>
        <p>You have the right to:</p>
        <ul>
          <li>Access the personal data we store about you</li>
          <li>Request correction or deletion of your data</li>
          <li>Request a copy of your data (data portability)</li>
          <li>Withdraw your consent at any time</li>
        </ul>

        <h3>7. Data processors</h3>
        <p>We may use secure third-party services (e.g., hosting or email providers) to store or process your data. These services comply with EU data protection standards.</p>

        <h3>8. Contact</h3>
        <p>For privacy questions or data requests, contact us at: <br>
        <strong>[<EMAIL>]</strong></p>
      </div>
    </div>
  </div>

  {# TERMS AND CONDITIONS MODAL #}
  <div id="terms-conditions-modal" style="display: none;">
    <style>
      me {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    </style>
    <div onclick="event.stopPropagation()">
      <style>
        me {
          all: unset;
          width: calc(100vw - 100px);
          max-width: 720px;
          max-height: 80vh;
          border: 1px solid var(--color-background-dark);
          border-radius: 11px;
          display: block;
          padding: 0;
          font-family: 'Noto Sans', sans-serif;
          background-color: var(--color-background-bright);
          position: relative;
        }
      </style>

      {# CLOSE BUTTON #}
      <div onclick="hideTermsConditions()">
        <style>
          me {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
          }

          me .close-x {
            width: 26px;
            height: 2px;
            background-color: var(--color-text-dark);
            position: relative;
            transform: rotate(45deg);
            transition: all 0.3s ease;
          }

          me .close-x::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 2px;
            background-color: var(--color-text-dark);
            left: 0;
            top: 0;
            transform: rotate(-90deg);
            transition: all 0.3s ease;
          }

          me:hover .close-x,
          me:hover .close-x::after {
            background-color: greenyellow;
          }
        </style>
        <div class="close-x"></div>
      </div>

      {# TITLE #}
      <div>
        <style>
          me {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            background-color: var(--color-background-bright);
            color: var(--color-text-dark);
            height: 40px;
            text-align: center;
            line-height: 40px;
            vertical-align: middle;
            font-family: 'Noto Serif', serif;
            font-size: 20px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 0 8px;
          }
        </style>
        Terms and Conditions
      </div>

      {# CONTENT #}
      <div>
        <style>
          me {
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
            background-color: var(--color-background-bright);
            padding: 15px 20px;
            text-align: left;
            font-size: 16px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            color: var(--color-text-black);
            max-height: calc(80vh - 80px);
            overflow-y: auto;
          }

          me h2 {
            color: var(--color-text-dark);
            font-family: 'Noto Serif', serif;
            font-size: 22px;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 16px;
          }

          me h3 {
            color: var(--color-text-dark);
            font-family: 'Noto Serif', serif;
            font-size: 18px;
            font-weight: 500;
            margin-top: 20px;
            margin-bottom: 12px;
          }

          me p {
            margin-bottom: 12px;
            line-height: 1.5;
          }

          me ul {
            margin-bottom: 12px;
            padding-left: 20px;
          }

          me li {
            margin-bottom: 6px;
            line-height: 1.4;
          }
        </style>
        <h2>Terms and Conditions</h2>
        <p><strong>Last updated:</strong> 10 July 2025</p>

        <h3>1. Use of Service</h3>
        <p>This service is provided to help users manage and generate PDF reports, including optional information about their own customers. You are responsible for the accuracy of the data you enter and for using the service lawfully.</p>

        <h3>2. Account Responsibilities</h3>
        <ul>
          <li>You agree to keep your login information secure.</li>
          <li>You are responsible for all activity under your account.</li>
          <li>You must not use the service for any unlawful, abusive, or unauthorized purpose.</li>
        </ul>

        <h3>3. Customer Data</h3>
        <p>If you provide personal data about your customers, you confirm that you have the right to do so under applicable data protection laws. We act only as a processor of that data on your behalf and do not access or use it ourselves.</p>

        <h3>4. Data Deletion</h3>
        <p>You may delete your account and data at any time. We will permanently remove your personal and customer-related data upon request.</p>

        <h3>5. Service Availability</h3>
        <p>We aim to provide a reliable service but do not guarantee uninterrupted availability. The service is provided "as is" without warranties.</p>

        <h3>6. Limitation of Liability</h3>
        <p>We are not liable for any damages or data loss arising from the use or inability to use the service.</p>

        <h3>7. Changes to These Terms</h3>
        <p>We may update these terms from time to time. Continued use of the service after changes means you accept the updated terms.</p>

        <h3>8. Contact</h3>
        <p>For any questions, contact us at: <br>
        <strong>[<EMAIL>]</strong></p>
      </div>
    </div>
  </div>

  {# JAVASCRIPT FOR MODAL FUNCTIONALITY #}
  <script>
    function showPrivacyPolicy() {
      document.getElementById('privacy-policy-modal').style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }

    function hidePrivacyPolicy() {
      document.getElementById('privacy-policy-modal').style.display = 'none';
      document.body.style.overflow = 'auto';
    }

    function showTermsConditions() {
      document.getElementById('terms-conditions-modal').style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }

    function hideTermsConditions() {
      document.getElementById('terms-conditions-modal').style.display = 'none';
      document.body.style.overflow = 'auto';
    }

    // Close modals when clicking outside
    document.getElementById('privacy-policy-modal').addEventListener('click', hidePrivacyPolicy);
    document.getElementById('terms-conditions-modal').addEventListener('click', hideTermsConditions);

    // Close modals with Escape key
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        hidePrivacyPolicy();
        hideTermsConditions();
      }
    });

    // Initialize form validation on page load
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
        let email = document.getElementById('signupemail');
        let pass = document.getElementById('signuppass');
        let passrepeat = document.getElementById('signuppassrepeat');
        let terms = document.getElementById('terms-checkbox');

        if (email && pass && passrepeat && terms) {
          // Trigger initial validation using Datastar signals
          let isValid = email.checkValidity() && pass.checkValidity() && passrepeat.checkValidity() && terms.checked;
          if (window.ds && window.ds.signals) {
            window.ds.signals._form_invalid = !isValid;
          }
        }
      }, 100);
    });




  </script>

</div>
{% endblock content %}
